import { Component, Input, Output, EventEmitter, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { register } from 'swiper/element/bundle';

// Register Swiper custom elements
register();

export interface Story {
  _id: string;
  user: {
    _id: string;
    username: string;
    fullName: string;
    avatar?: string;
  };
  media: {
    type: 'image' | 'video';
    url: string;
  }[];
  viewed?: boolean;
  createdAt: Date;
}

export interface CurrentUser {
  _id: string;
  username: string;
  fullName: string;
  avatar?: string;
}

@Component({
  selector: 'app-view-add-stories',
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <div class="stories-container">
      <swiper-container 
        class="stories-swiper"
        [slides-per-view]="slidesPerView"
        [space-between]="spaceBetween"
        free-mode="true"
        grab-cursor="true"
        mousewheel="true"
        keyboard="true"
        [breakpoints]="swiperBreakpoints">
        
        <!-- Add Story Slide -->
        <swiper-slide class="story-slide" *ngIf="showAddStory">
          <div class="story-item add-story" (click)="onCreateStory()">
            <div class="story-avatar-container">
              <div class="story-avatar add-avatar">
                <img [src]="currentUser?.avatar || defaultAvatar"
                     [alt]="currentUser?.fullName || 'Your Profile'"
                     class="profile-image"
                     (error)="onImageError($event)">
              </div>
              <div class="add-story-plus">
                <i class="fas fa-plus"></i>
              </div>
            </div>
            <span class="story-username">{{ addStoryText }}</span>
          </div>
        </swiper-slide>

        <!-- Story Slides -->
        <swiper-slide 
          class="story-slide"
          *ngFor="let story of stories; let i = index">
          <div class="story-item" (click)="onStoryClick(story, i)">
            <div class="story-avatar-container">
              <div class="story-avatar" [class.viewed]="story.viewed">
                <div class="story-ring" [class.viewed]="story.viewed"></div>
                <img [src]="story.user.avatar || defaultAvatar"
                     [alt]="story.user.fullName"
                     class="profile-image"
                     (error)="onImageError($event)">
              </div>
            </div>
            <span class="story-username">{{ story.user.username }}</span>
          </div>
        </swiper-slide>
      </swiper-container>
    </div>
  `,
  styles: [`
    .stories-container {
      width: 100%;
      padding: 20px 0;
      background: #fff;
      border-bottom: 1px solid #dbdbdb;
    }

    .stories-swiper {
      width: 100%;
      height: auto;
      padding: 0 20px;
    }

    .story-slide {
      width: auto !important;
      flex-shrink: 0;
    }

    .story-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      transition: transform 0.2s ease;
      min-width: 80px;
    }

    .story-item:hover {
      transform: scale(1.05);
    }

    .story-avatar-container {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .story-avatar {
      width: 66px;
      height: 66px;
      border-radius: 50%;
      overflow: hidden;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fff;
    }

    .story-ring {
      position: absolute;
      top: -3px;
      left: -3px;
      right: -3px;
      bottom: -3px;
      border-radius: 50%;
      background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
      padding: 3px;
      z-index: 1;
    }

    .story-ring.viewed {
      background: #c7c7c7;
    }

    .story-avatar .profile-image {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid #fff;
      position: relative;
      z-index: 2;
    }

    /* Add Story Specific Styles */
    .add-story .story-avatar-container {
      position: relative;
    }

    .add-story .story-avatar {
      width: 66px;
      height: 66px;
      border: 3px solid #dbdbdb;
      background: #fff;
    }

    .add-story .profile-image {
      width: 60px;
      height: 60px;
      border: none;
    }

    .add-story-plus {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 24px;
      height: 24px;
      background: #0095f6;
      border: 3px solid #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 12px;
      font-weight: bold;
      z-index: 3;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .add-story-plus i {
      font-size: 12px;
      line-height: 1;
    }

    .story-username {
      font-size: 12px;
      color: #262626;
      text-align: center;
      max-width: 80px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-weight: 400;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .stories-swiper {
        padding: 0 16px;
      }

      .story-avatar {
        width: 56px;
        height: 56px;
      }

      .story-avatar .profile-image {
        width: 50px;
        height: 50px;
      }

      .add-story .story-avatar {
        width: 56px;
        height: 56px;
      }

      .add-story .profile-image {
        width: 50px;
        height: 50px;
      }

      .add-story-plus {
        width: 20px;
        height: 20px;
        font-size: 10px;
      }

      .story-username {
        font-size: 11px;
        max-width: 70px;
      }

      .story-item {
        min-width: 70px;
      }
    }

    @media (max-width: 480px) {
      .story-avatar {
        width: 50px;
        height: 50px;
      }

      .story-avatar .profile-image {
        width: 44px;
        height: 44px;
      }

      .add-story .story-avatar {
        width: 50px;
        height: 50px;
      }

      .add-story .profile-image {
        width: 44px;
        height: 44px;
      }

      .story-item {
        min-width: 60px;
        gap: 6px;
      }

      .add-story-plus {
        width: 18px;
        height: 18px;
        font-size: 9px;
      }

      .story-username {
        font-size: 10px;
        max-width: 60px;
      }

      .stories-swiper {
        padding: 0 12px;
      }
    }
  `]
})
export class ViewAddStoriesComponent implements OnInit {
  @Input() stories: Story[] = [];
  @Input() showAddStory: boolean = true;
  @Input() addStoryText: string = 'Your Story';
  @Input() defaultAvatar: string = '/assets/images/default-avatar.svg';
  @Input() slidesPerView: string = 'auto';
  @Input() spaceBetween: number = 16;
  @Input() currentUser: CurrentUser | null = null;

  @Output() storyClick = new EventEmitter<{story: Story, index: number}>();
  @Output() createStory = new EventEmitter<void>();

  swiperBreakpoints = {
    320: {
      slidesPerView: 'auto',
      spaceBetween: 12
    },
    768: {
      slidesPerView: 'auto',
      spaceBetween: 16
    }
  };

  ngOnInit() {
    console.log('View Add Stories - Current User:', this.currentUser);
    console.log('View Add Stories - Show Add Story:', this.showAddStory);
    console.log('View Add Stories - Default Avatar:', this.defaultAvatar);
  }

  onStoryClick(story: Story, index: number) {
    this.storyClick.emit({ story, index });
  }

  onCreateStory() {
    this.createStory.emit();
  }

  onImageError(event: any) {
    event.target.src = this.defaultAvatar;
  }
}
