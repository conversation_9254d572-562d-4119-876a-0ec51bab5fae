.featured-brands-container {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  margin-bottom: 24px;
  color: white;
}

// Header Section
.section-header {
  margin-bottom: 24px;
  
  .header-content {
    text-align: center;
  }
  
  .section-title {
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    
    .title-icon {
      font-size: 28px;
      color: #ffd700;
    }
  }
  
  .section-subtitle {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
  }
}

// Loading State
.loading-container {
  .loading-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
  }
  
  .loading-brand-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 20px;
    backdrop-filter: blur(10px);
    
    .loading-header {
      margin-bottom: 16px;
      
      .loading-brand-name {
        height: 24px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        margin-bottom: 8px;
        animation: loading 1.5s infinite;
      }
      
      .loading-stats {
        height: 16px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        width: 70%;
        animation: loading 1.5s infinite;
      }
    }
    
    .loading-products {
      display: flex;
      gap: 12px;
      
      .loading-product {
        flex: 1;
        height: 120px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        animation: loading 1.5s infinite;
      }
    }
  }
}

@keyframes loading {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

// Error State
.error-container {
  text-align: center;
  padding: 40px 20px;
  
  .error-icon {
    font-size: 48px;
    color: #ff6b6b;
    margin-bottom: 16px;
  }
  
  .error-message {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;
  }
  
  .retry-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}

// Brands Slider
.brands-slider-container {
  position: relative;

  .brands-slider {
    .owl-nav {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 100%;
      pointer-events: none;

      .owl-prev,
      .owl-next {
        position: absolute;
        width: 40px;
        height: 40px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        border: none;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        pointer-events: all;
        transition: all 0.3s ease;
        z-index: 10;

        &:hover {
          background: rgba(0, 0, 0, 0.9);
          transform: scale(1.1);
        }
      }

      .owl-prev {
        left: -20px;
      }

      .owl-next {
        right: -20px;
      }
    }

    .owl-dots {
      display: none;
    }
  }
}

// Enhanced brand card styling
.brand-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 24px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;

  // Gradient overlay effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-12px) scale(1.02);
    background: rgba(255, 255, 255, 0.18);
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.3),
      0 8px 20px rgba(255, 255, 255, 0.1) inset;
    border-color: rgba(255, 255, 255, 0.3);

    &::before {
      opacity: 1;
    }
  }
}

// Enhanced brand header styling
.brand-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  position: relative;
  z-index: 1;

  .brand-info {
    flex: 1;
    padding-right: 16px;
  }

  .brand-name {
    font-size: 24px;
    font-weight: 800;
    color: white;
    margin: 0 0 16px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.5px;
  }

  .brand-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 13px;
      color: rgba(255, 255, 255, 0.9);
      background: rgba(255, 255, 255, 0.1);
      padding: 6px 12px;
      border-radius: 12px;
      backdrop-filter: blur(5px);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateX(4px);
      }

      ion-icon {
        font-size: 16px;
        color: #ffd700;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
      }

      span {
        font-weight: 600;
      }
    }
  }

  .brand-badge {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    padding: 10px 16px;
    border-radius: 25px;
    font-size: 13px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow:
      0 4px 15px rgba(255, 215, 0, 0.4),
      0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 6px 20px rgba(255, 215, 0, 0.5),
        0 4px 8px rgba(0, 0, 0, 0.3);
    }

    ion-icon {
      font-size: 16px;
      animation: sparkle 2s ease-in-out infinite;
    }
  }
}

@keyframes sparkle {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

// Enhanced top products section
.top-products {
  margin-bottom: 24px;
  position: relative;
  z-index: 1;

  .products-title {
    font-size: 18px;
    font-weight: 700;
    color: white;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

    &::before {
      content: '🔥';
      font-size: 20px;
    }
  }

  .products-list {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    padding: 8px 0 16px 0;
    scroll-behavior: smooth;

    // Enhanced scrollbar styling
    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(90deg, #ffd700, #ffed4e);
      border-radius: 3px;

      &:hover {
        background: linear-gradient(90deg, #ffed4e, #ffd700);
      }
    }
  }

  // Empty products state
  .no-products {
    text-align: center;
    padding: 40px 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 2px dashed rgba(255, 255, 255, 0.2);

    .empty-icon {
      font-size: 32px;
      color: rgba(255, 255, 255, 0.4);
      margin-bottom: 12px;
    }

    .empty-text {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.6);
      font-weight: 500;
    }
  }
}

// Enhanced product item styling
.product-item {
  flex: 0 0 160px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);

  &:hover {
    transform: translateY(-8px) scale(1.05);
    background: rgba(255, 255, 255, 0.18);
    box-shadow:
      0 12px 30px rgba(0, 0, 0, 0.3),
      0 4px 10px rgba(255, 255, 255, 0.1) inset;
    border-color: rgba(255, 255, 255, 0.2);

    .product-actions {
      opacity: 1;
      transform: translateY(0);
    }

    .product-image {
      transform: scale(1.1);
    }
  }
}

// Enhanced product image container
.product-image-container {
  position: relative;
  overflow: hidden;

  .product-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    transition: transform 0.4s ease;
  }

  // Gradient overlay for better text readability
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
    pointer-events: none;
  }

  .product-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 6px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    .action-btn {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      border: none;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(15px);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

      ion-icon {
        font-size: 16px;
        color: #333;
      }

      &:hover {
        background: white;
        transform: scale(1.15);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
      }

      &.like-btn {
        &:hover {
          background: rgba(220, 53, 69, 0.1);

          ion-icon {
            color: #dc3545;
          }
        }

        &.liked {
          background: rgba(220, 53, 69, 0.2);

          ion-icon {
            color: #dc3545;
          }
        }
      }

      &.share-btn:hover {
        background: rgba(0, 123, 255, 0.1);

        ion-icon {
          color: #007bff;
        }
      }
    }
  }
}

// Enhanced product details styling
.product-details {
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);

  .product-name {
    font-size: 13px;
    font-weight: 700;
    color: white;
    margin: 0 0 10px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .product-price {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;

    .current-price {
      font-size: 16px;
      font-weight: 800;
      color: #ffd700;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .original-price {
      font-size: 11px;
      color: rgba(255, 255, 255, 0.6);
      text-decoration: line-through;
      background: rgba(255, 255, 255, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
    }
  }

  .product-rating {
    display: flex;
    align-items: center;
    gap: 8px;

    .stars {
      display: flex;
      gap: 2px;

      ion-icon {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.3);
        transition: color 0.3s ease;

        &.filled {
          color: #ffd700;
          filter: drop-shadow(0 1px 2px rgba(255, 215, 0, 0.5));
        }
      }
    }

    .rating-count {
      font-size: 11px;
      color: rgba(255, 255, 255, 0.7);
      background: rgba(255, 255, 255, 0.1);
      padding: 2px 6px;
      border-radius: 8px;
      font-weight: 600;
    }
  }
}

// Enhanced view more section
.view-more-section {
  position: relative;
  z-index: 1;

  .view-more-btn {
    width: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 16px 20px;
    border-radius: 16px;
    font-weight: 700;
    font-size: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

    // Animated background effect
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
      border-color: rgba(255, 255, 255, 0.4);
      transform: translateY(-4px);
      box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(255, 255, 255, 0.1) inset;

      &::before {
        left: 100%;
      }

      ion-icon {
        transform: translateX(4px);
      }
    }

    &:active {
      transform: translateY(-2px);
    }

    ion-icon {
      font-size: 18px;
      transition: transform 0.3s ease;
    }

    span {
      position: relative;
      z-index: 1;
    }
  }
}

// Empty State
.empty-container {
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 64px;
    color: rgba(255, 255, 255, 0.4);
    margin-bottom: 20px;
  }
  
  .empty-title {
    font-size: 20px;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
  }
  
  .empty-message {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }
}

// Enhanced Responsive Design
@media (max-width: 1024px) {
  .brands-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 18px;
  }

  .brand-card {
    padding: 20px;
  }

  .brand-name {
    font-size: 22px;
  }
}

@media (max-width: 768px) {
  .featured-brands-container {
    padding: 16px;
  }

  .brands-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .brand-card {
    padding: 18px;

    &:hover {
      transform: translateY(-6px) scale(1.01);
    }
  }

  .brand-header {
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;

    .brand-info {
      padding-right: 0;
    }

    .brand-badge {
      align-self: flex-start;
      padding: 8px 14px;
      font-size: 12px;
    }

    .brand-name {
      font-size: 20px;
      margin-bottom: 12px;
    }
  }

  .brand-stats {
    flex-direction: row !important;
    flex-wrap: wrap;
    gap: 8px !important;

    .stat-item {
      font-size: 12px;
      padding: 4px 8px;
    }
  }

  .products-title {
    font-size: 16px;
  }

  .product-item {
    flex: 0 0 140px;
  }

  .section-title {
    font-size: 20px;
  }

  .view-more-btn {
    padding: 14px 18px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .brand-card {
    padding: 16px;
  }

  .brand-name {
    font-size: 18px;
  }

  .product-item {
    flex: 0 0 120px;
  }

  .product-image {
    height: 100px;
  }

  .product-details {
    padding: 12px;
  }
}
