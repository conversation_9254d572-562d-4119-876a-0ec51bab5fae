# Stories Carousel Component

A reusable Angular component that displays stories in a horizontal carousel using Swiper.js. Features smooth scrolling, touch/mouse support, and responsive design.

## Features

- **Swiper.js Integration**: Smooth horizontal scrolling with touch/mouse support
- **Responsive Design**: Adapts to different screen sizes
- **Animated Story Rings**: Gradient rings with rotation animation for unviewed stories
- **Add Story Button**: Optional "Add Story" button with customizable text
- **TypeScript Support**: Fully typed with interfaces
- **Standalone Component**: Can be used anywhere in the application

## Usage

### Basic Usage

```typescript
import { StoriesCarouselComponent, Story } from './shared/components/stories-carousel/stories-carousel.component';

@Component({
  imports: [StoriesCarouselComponent],
  template: `
    <app-stories-carousel
      [stories]="stories"
      (storyClick)="onStoryClick($event)"
      (createStory)="onCreateStory()">
    </app-stories-carousel>
  `
})
export class MyComponent {
  stories: Story[] = [
    {
      _id: '1',
      user: {
        _id: 'user1',
        username: 'john_doe',
        fullName: '<PERSON>',
        avatar: '/assets/avatars/john.jpg'
      },
      media: [{
        type: 'image',
        url: '/assets/stories/story1.jpg'
      }],
      viewed: false,
      createdAt: new Date()
    }
  ];

  onStoryClick(event: {story: Story, index: number}) {
    console.log('Story clicked:', event.story);
    // Navigate to story viewer
  }

  onCreateStory() {
    console.log('Create story clicked');
    // Navigate to story creation
  }
}
```

### Advanced Usage

```typescript
<app-stories-carousel
  [stories]="stories"
  [showAddStory]="true"
  addStoryText="Create Story"
  defaultAvatar="/assets/default-avatar.png"
  [slidesPerView]="'auto'"
  [spaceBetween]="20"
  (storyClick)="onStoryClick($event)"
  (createStory)="onCreateStory()">
</app-stories-carousel>
```

## Input Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `stories` | `Story[]` | `[]` | Array of story objects to display |
| `showAddStory` | `boolean` | `true` | Whether to show the "Add Story" button |
| `addStoryText` | `string` | `'Your Story'` | Text to display under the add story button |
| `defaultAvatar` | `string` | `'/assets/images/default-avatar.png'` | Default avatar image path |
| `slidesPerView` | `string` | `'auto'` | Number of slides to show (Swiper option) |
| `spaceBetween` | `number` | `16` | Space between slides in pixels |

## Output Events

| Event | Type | Description |
|-------|------|-------------|
| `storyClick` | `{story: Story, index: number}` | Emitted when a story is clicked |
| `createStory` | `void` | Emitted when the "Add Story" button is clicked |

## Story Interface

```typescript
interface Story {
  _id: string;
  user: {
    _id: string;
    username: string;
    fullName: string;
    avatar?: string;
  };
  media: {
    type: 'image' | 'video';
    url: string;
  }[];
  viewed?: boolean;
  createdAt: Date;
}
```

## Styling

The component includes responsive styles that adapt to different screen sizes:

- **Desktop**: 60px avatars with 16px spacing
- **Tablet**: 50px avatars with 12px spacing  
- **Mobile**: 45px avatars with reduced spacing

### Custom Styling

You can override the default styles by targeting the component's CSS classes:

```css
app-stories-carousel {
  .story-avatar {
    width: 80px;
    height: 80px;
  }
  
  .story-ring {
    background: linear-gradient(45deg, #your-color, #your-color);
  }
}
```

## Dependencies

- **Swiper**: For carousel functionality
- **Angular 17+**: Standalone component support
- **Font Awesome**: For the plus icon (optional)

## Installation

The component is already included in the DFashion project. To use it in other projects:

1. Install Swiper: `npm install swiper`
2. Copy the component files
3. Import and use in your components

## Browser Support

- Modern browsers with ES6+ support
- Touch devices (iOS, Android)
- Desktop with mouse/keyboard navigation
