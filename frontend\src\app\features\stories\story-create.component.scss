.story-create-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.create-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(0,0,0,0.8);
  color: #fff;
}

.btn-back, .btn-share {
  background: none;
  border: none;
  color: #fff;
  font-size: 1rem;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 20px;
  transition: background 0.2s ease;
}

.btn-back:hover {
  background: rgba(255,255,255,0.1);
}

.btn-share {
  background: #007bff;
  font-weight: 600;
}

.btn-share:disabled {
  background: #666;
  cursor: not-allowed;
}

.btn-share:not(:disabled):hover {
  background: #0056b3;
}

.create-header h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.media-selection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.selection-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
  max-width: 400px;
  width: 100%;
}

.option-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 30px 20px;
  background: rgba(255,255,255,0.1);
  border-radius: 16px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.option-card:hover {
  background: rgba(255,255,255,0.2);
  border-color: #007bff;
  transform: translateY(-4px);
}

.option-card i {
  font-size: 2rem;
}

.option-card span {
  font-weight: 500;
}

.media-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.preview-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.preview-media {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.story-tools {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 16px;
  background: rgba(0,0,0,0.5);
}

.tool-btn {
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  background: rgba(255,255,255,0.2);
  color: #fff;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-btn:hover,
.tool-btn.active {
  background: #007bff;
  transform: scale(1.1);
}

.caption-section {
  padding: 16px 20px;
  background: rgba(0,0,0,0.8);
  position: relative;
}

.caption-input {
  width: 100%;
  min-height: 60px;
  background: rgba(255,255,255,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  border-radius: 12px;
  padding: 12px;
  color: #fff;
  font-size: 0.9rem;
  resize: none;
  outline: none;
}

.caption-input::placeholder {
  color: rgba(255,255,255,0.6);
}

.char-count {
  position: absolute;
  bottom: 20px;
  right: 24px;
  font-size: 0.8rem;
  color: rgba(255,255,255,0.6);
}

.product-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.8);
  z-index: 1100;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-content {
  background: #fff;
  border-radius: 12px;
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px;
}

.product-search {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.search-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 0.9rem;
  outline: none;
}

.search-input:focus {
  border-color: #007bff;
}

.products-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.product-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.product-item:hover {
  background: #f8f9fa;
}

.product-image {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 6px;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-name {
  font-weight: 500;
  font-size: 0.9rem;
}

.product-price {
  color: #007bff;
  font-weight: 600;
  font-size: 0.9rem;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.8);
  z-index: 1200;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
  color: #fff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255,255,255,0.3);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .create-header {
    padding: 12px 16px;
  }

  .create-header h2 {
    font-size: 1.1rem;
  }

  .selection-options {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }

  .option-card {
    padding: 24px 16px;
  }

  .option-card i {
    font-size: 1.8rem;
  }

  .story-tools {
    gap: 12px;
    padding: 12px;
  }

  .tool-btn {
    width: 44px;
    height: 44px;
    font-size: 1.1rem;
  }

  .caption-section {
    padding: 12px 16px;
  }
}

@media (max-width: 480px) {
  .selection-options {
    grid-template-columns: 1fr;
    max-width: 200px;
  }

  .option-card {
    padding: 20px;
  }

  .modal-content {
    margin: 10px;
    max-width: calc(100vw - 20px);
  }
}
