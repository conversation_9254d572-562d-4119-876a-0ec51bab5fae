import { <PERSON>mpo<PERSON>, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatBadgeModule } from '@angular/material/badge';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RealtimeNotificationService, RealtimeNotification } from '../../../core/services/realtime-notification.service';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';

@Component({
  selector: 'app-realtime-notifications',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatBadgeModule,
    MatMenuModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatTooltipModule
  ],
  templateUrl: './realtime-notifications.component.html',
  styles: [`
    .notification-bell {
      position: relative;
    }

    .notification-button {
      transition: all 0.3s ease;
    }

    .notification-button.has-notifications {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
    }

    .notification-menu {
      width: 400px;
      max-height: 600px;
    }

    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background: #f5f5f5;
    }

    .notification-header h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .connection-status {
      display: flex;
      align-items: center;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.3s ease;
    }

    .connection-status.connected {
      color: #4caf50;
    }

    .connection-status.disconnected {
      color: #f44336;
    }

    .notification-list {
      max-height: 400px;
      overflow-y: auto;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32px;
      gap: 16px;
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32px;
      color: #666;
    }

    .empty-state mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .notification-item {
      display: flex;
      align-items: flex-start;
      padding: 12px 16px;
      border-bottom: 1px solid #eee;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
    }

    .notification-item:hover {
      background: #f9f9f9;
    }

    .notification-item.unread {
      background: #e3f2fd;
      border-left: 4px solid #2196f3;
    }

    .notification-item.urgent {
      border-left-color: #f44336;
    }

    .notification-item.high {
      border-left-color: #ff9800;
    }

    .notification-icon {
      margin-right: 12px;
      margin-top: 4px;
    }

    .notification-content {
      flex: 1;
      min-width: 0;
    }

    .notification-title {
      font-weight: 600;
      font-size: 14px;
      margin-bottom: 4px;
      color: #333;
    }

    .notification-message {
      font-size: 13px;
      color: #666;
      line-height: 1.4;
      margin-bottom: 8px;
      word-wrap: break-word;
    }

    .notification-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 11px;
      color: #999;
    }

    .notification-category {
      background: #e0e0e0;
      padding: 2px 6px;
      border-radius: 10px;
      text-transform: uppercase;
      font-weight: 500;
    }

    .notification-actions {
      margin-left: 8px;
    }

    .mark-read-btn {
      width: 32px;
      height: 32px;
      line-height: 32px;
    }

    .notification-footer {
      padding: 16px;
      text-align: center;
      border-top: 1px solid #eee;
      background: #fafafa;
    }

    .view-all-btn {
      width: 100%;
      color: #2196f3;
      font-weight: 500;
    }

    .mark-all-read-btn {
      color: #2196f3;
    }

    /* Scrollbar styling */
    .notification-list::-webkit-scrollbar {
      width: 6px;
    }

    .notification-list::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    .notification-list::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    .notification-list::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  `]
})
export class RealtimeNotificationsComponent implements OnInit, OnDestroy {
  notifications: RealtimeNotification[] = [];
  unreadCount = 0;
  isConnected = false;
  loading = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private realtimeNotificationService: RealtimeNotificationService,
    private router: Router
  ) {}

  ngOnInit() {
    this.subscribeToNotifications();
    this.loadInitialData();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private subscribeToNotifications() {
    // Subscribe to notifications
    this.subscriptions.push(
      this.realtimeNotificationService.notifications$.subscribe(notifications => {
        this.notifications = notifications.slice(0, 10); // Show only latest 10 in dropdown
        this.loading = false;
      })
    );

    // Subscribe to unread count
    this.subscriptions.push(
      this.realtimeNotificationService.unreadCount$.subscribe(count => {
        this.unreadCount = count;
      })
    );

    // Subscribe to connection status
    this.subscriptions.push(
      this.realtimeNotificationService.connectionStatus$.subscribe(status => {
        this.isConnected = status;
      })
    );
  }

  private loadInitialData() {
    this.loading = true;
    this.realtimeNotificationService.getNotifications({ page: 1, limit: 10 });
  }

  markAsRead(notificationId: string, event?: Event) {
    if (event) {
      event.stopPropagation();
    }
    this.realtimeNotificationService.markAsRead(notificationId);
  }

  markAllAsRead() {
    this.realtimeNotificationService.markAllAsRead();
  }

  handleNotificationClick(notification: RealtimeNotification) {
    // Mark as read if not already read
    if (!notification.isRead) {
      this.markAsRead(notification._id);
    }

    // Navigate based on notification type
    this.navigateToRelatedContent(notification);
  }

  private navigateToRelatedContent(notification: RealtimeNotification) {
    const { type, relatedEntity, data } = notification;

    switch (type) {
      case 'order_placed':
      case 'order_confirmed':
      case 'order_shipped':
      case 'order_delivered':
      case 'order_cancelled':
        if (data?.orderId) {
          this.router.navigate(['/orders', data.orderId]);
        }
        break;

      case 'payment_success':
      case 'payment_failed':
        if (data?.orderId) {
          this.router.navigate(['/orders', data.orderId]);
        }
        break;

      case 'product_liked':
      case 'product_commented':
        if (data?.productId) {
          this.router.navigate(['/products', data.productId]);
        }
        break;

      case 'user_followed':
        if (data?.userId) {
          this.router.navigate(['/profile', data.userId]);
        }
        break;

      case 'post_liked':
      case 'post_commented':
        if (data?.postId) {
          this.router.navigate(['/posts', data.postId]);
        }
        break;

      default:
        // For other notifications, go to notifications page
        this.router.navigate(['/notifications']);
        break;
    }
  }

  viewAllNotifications() {
    this.router.navigate(['/notifications']);
  }

  getNotificationIcon(type: string): string {
    return this.realtimeNotificationService.getNotificationIcon(type);
  }

  getNotificationColor(priority: string): string {
    return this.realtimeNotificationService.getNotificationColor(priority);
  }

  trackByNotificationId(index: number, notification: RealtimeNotification): string {
    return notification._id;
  }
}
