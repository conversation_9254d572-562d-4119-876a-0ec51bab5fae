<div class="new-arrivals-container">
  <!-- Header -->
  <div class="section-header">
    <div class="header-content">
      <h2 class="section-title">
        <ion-icon name="sparkles" class="title-icon"></ion-icon>
        New Arrivals
      </h2>
      <p class="section-subtitle">Fresh styles just landed</p>
    </div>
    <button class="view-all-btn" (click)="onViewAll()">
      View All
      <ion-icon name="chevron-forward"></ion-icon>
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-grid">
      <div *ngFor="let item of [1,2,3,4,5,6]" class="loading-card">
        <div class="loading-image"></div>
        <div class="loading-content">
          <div class="loading-line short"></div>
          <div class="loading-line medium"></div>
          <div class="loading-line long"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !isLoading" class="error-container">
    <ion-icon name="alert-circle" class="error-icon"></ion-icon>
    <p class="error-message">{{ error }}</p>
    <button class="retry-btn" (click)="onRetry()">
      <ion-icon name="refresh"></ion-icon>
      Try Again
    </button>
  </div>

  <!-- Products Slider -->
  <div *ngIf="!isLoading && !error && newArrivals.length > 0" class="products-slider-container">
    <owl-carousel-o [options]="carouselOptions" class="products-slider">
      <ng-template carouselSlide *ngFor="let product of newArrivals; trackBy: trackByProductId">
        <div
          class="product-card"
          (click)="onProductClick(product)"
        >
      <!-- Product Image -->
      <div class="product-image-container">
        <img 
          [src]="product.images[0].url"
          [alt]="product.images[0].alt || product.name"
          class="product-image"
          loading="lazy"
        />
        
        <!-- New Badge -->
        <div class="new-badge">
          <ion-icon name="sparkles"></ion-icon>
          New
        </div>

        <!-- Days Badge -->
        <div class="days-badge">
          {{ getDaysAgo(product.createdAt) }} days ago
        </div>

        <!-- Discount Badge -->
        <div *ngIf="getDiscountPercentage(product) > 0" class="discount-badge">
          {{ getDiscountPercentage(product) }}% OFF
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <button
            class="action-btn like-btn"
            [class.liked]="isProductLiked(product._id)"
            (click)="onLikeProduct(product, $event)"
            [attr.aria-label]="'Like ' + product.name"
          >
            <ion-icon [name]="isProductLiked(product._id) ? 'heart' : 'heart-outline'"></ion-icon>
          </button>
          <button 
            class="action-btn share-btn" 
            (click)="onShareProduct(product, $event)"
            [attr.aria-label]="'Share ' + product.name"
          >
            <ion-icon name="share-outline"></ion-icon>
          </button>
        </div>
      </div>

      <!-- Product Info -->
      <div class="product-info">
        <div class="product-brand">{{ product.brand }}</div>
        <h3 class="product-name">{{ product.name }}</h3>
        
        <!-- Price Section -->
        <div class="price-section">
          <span class="current-price">{{ formatPrice(product.price) }}</span>
          <span *ngIf="product.originalPrice && product.originalPrice > product.price" 
                class="original-price">{{ formatPrice(product.originalPrice) }}</span>
        </div>

        <!-- Rating -->
        <div class="rating-section">
          <div class="stars">
            <ion-icon 
              *ngFor="let star of [1,2,3,4,5]" 
              [name]="star <= product.rating.average ? 'star' : 'star-outline'"
              [class.filled]="star <= product.rating.average"
            ></ion-icon>
          </div>
          <span class="rating-text">({{ product.rating.count }})</span>
        </div>

        <!-- Action Buttons -->
        <div class="product-actions">
          <button 
            class="cart-btn" 
            (click)="onAddToCart(product, $event)"
          >
            <ion-icon name="bag-add-outline"></ion-icon>
            Add to Cart
          </button>
          <button 
            class="wishlist-btn" 
            (click)="onAddToWishlist(product, $event)"
          >
            <ion-icon name="heart-outline"></ion-icon>
          </button>
        </div>
      </div>
        </div>
      </ng-template>
    </owl-carousel-o>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && !error && newArrivals.length === 0" class="empty-container">
    <ion-icon name="sparkles-outline" class="empty-icon"></ion-icon>
    <h3 class="empty-title">No New Arrivals</h3>
    <p class="empty-message">Check back soon for fresh new styles</p>
  </div>
</div>
