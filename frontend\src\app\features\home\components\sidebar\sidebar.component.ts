import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { OwlOptions } from 'ngx-owl-carousel-o';

import { ProductService } from '../../../../core/services/product.service';
import { Product } from '../../../../core/models/product.model';
import { TrendingProductsComponent } from '../trending-products/trending-products.component';
import { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';
import { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    IonicModule,
    CarouselModule,
    TrendingProductsComponent,
    FeaturedBrandsComponent,
    NewArrivalsComponent
  ],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent implements OnInit {
  suggestedUsers: any[] = [];
  trendingProducts: Product[] = [];
  topInfluencers: any[] = [];
  categories: any[] = [];
  isLoadingInfluencers: boolean = false;
  isLoadingSuggestions: boolean = false;

  // Carousel Options for different sections
  suggestionsCarouselOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: false,
    dots: false,
    navSpeed: 700,
    navText: ['<', '>'],
    nav: true,
    autoplay: true,
    autoplayTimeout: 6000,
    autoplayHoverPause: true,
    margin: 2,
    responsive: {
      0: {
        items: 1
      },
      600: {
        items: 2
      },
      900: {
        items: 3
      }
    }
  };

  influencersCarouselOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: false,
    dots: false,
    navSpeed: 700,
    navText: ['<', '>'],
    nav: true,
    autoplay: true,
    autoplayTimeout: 7000,
    autoplayHoverPause: true,
    margin: 2,
    responsive: {
      0: {
        items: 1
      },
      600: {
        items: 1.5
      },
      900: {
        items: 2
      }
    }
  };

  categoriesCarouselOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: false,
    dots: false,
    navSpeed: 700,
    navText: ['<', '>'],
    nav: true,
    autoplay: true,
    autoplayTimeout: 5000,
    autoplayHoverPause: true,
    margin: 2,
    responsive: {
      0: {
        items: 2
      },
      600: {
        items: 3
      },
      900: {
        items: 4
      }
    }
  };

  constructor(
    private productService: ProductService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadSuggestedUsers();
    this.loadTrendingProducts();
    this.loadTopInfluencers();
    this.loadCategories();
  }

  loadSuggestedUsers() {
    this.isLoadingSuggestions = true;

    // Load from real API - using ProductService for now
    this.productService.getSuggestedUsers().subscribe({
      next: (response) => {
        this.suggestedUsers = response?.data || [];
        this.isLoadingSuggestions = false;
      },
      error: (error) => {
        console.error('Error loading suggested users:', error);
        this.suggestedUsers = [];
        this.isLoadingSuggestions = false;
      }
    });
  }

  loadTrendingProducts() {
    // Load from API - empty for now
    this.trendingProducts = [];
  }

  loadTopInfluencers() {
    this.isLoadingInfluencers = true;

    // Load from real API
    this.productService.getTopInfluencers().subscribe({
      next: (response) => {
        this.topInfluencers = response?.data || [];
        this.isLoadingInfluencers = false;
      },
      error: (error) => {
        console.error('Error loading top influencers:', error);
        this.topInfluencers = [];
        this.isLoadingInfluencers = false;
      }
    });
  }

  loadCategories() {
    // Load from real API
    this.productService.getCategories().subscribe({
      next: (response) => {
        this.categories = response?.data || [];
      },
      error: (error) => {
        console.error('Error loading categories:', error);
        this.categories = [];
      }
    });
  }

  formatFollowerCount(count: number): string {
    if (count >= 1000) {
      return (count / 1000).toFixed(1) + 'k';
    }
    return count.toString();
  }

  followUser(userId: string) {
    const user = this.suggestedUsers.find(u => u.id === userId);
    if (user) {
      user.isFollowing = !user.isFollowing;
    }
  }

  followInfluencer(influencerId: string) {
    const influencer = this.topInfluencers.find(i => i.id === influencerId);
    if (influencer) {
      influencer.isFollowing = !influencer.isFollowing;
    }
  }

  quickBuy(productId: string) {
    console.log('Quick buy product:', productId);
    // TODO: Implement quick buy functionality
  }

  browseCategory(categorySlug: string) {
    console.log('Browse category:', categorySlug);
    this.router.navigate(['/category', categorySlug]);
  }
}
