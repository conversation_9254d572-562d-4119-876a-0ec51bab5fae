<aside class="sidebar">
  <!-- Instagram-style Stories -->
 

  <!-- Trending Products Section -->
  <app-trending-products></app-trending-products>

  <!-- Featured Brands Section -->
  <app-featured-brands></app-featured-brands>

  <!-- New Arrivals Section -->
  <app-new-arrivals></app-new-arrivals>

  <!-- Suggested for you -->
  <div class="suggestions">
    <h3>Suggested for you</h3>

    <!-- Loading State -->
    <div *ngIf="isLoadingSuggestions" class="loading-container">
      <div class="loading-card" *ngFor="let item of [1,2,3]">
        <div class="loading-content">
          <div class="loading-line short"></div>
          <div class="loading-line medium"></div>
          <div class="loading-line long"></div>
        </div>
      </div>
    </div>

    <!-- Suggestions Slider -->
    <div *ngIf="!isLoadingSuggestions && suggestedUsers.length > 0" class="suggestions-slider-container">
      <owl-carousel-o [options]="suggestionsCarouselOptions" class="suggestions-slider">
        <ng-template carouselSlide *ngFor="let user of suggestedUsers">
          <div class="suggestion-item">
            <img [src]="user.avatar" [alt]="user.fullName">
            <div class="suggestion-info">
              <h5>{{ user.username }}</h5>
              <p>{{ user.followedBy }}</p>
            </div>
            <button class="follow-btn" (click)="followUser(user.id)">
              {{ user.isFollowing ? 'Following' : 'Follow' }}
            </button>
          </div>
        </ng-template>
      </owl-carousel-o>
    </div>

    <!-- Empty State -->
    <div *ngIf="!isLoadingSuggestions && suggestedUsers.length === 0" class="empty-container">
      <ion-icon name="people-outline" class="empty-icon"></ion-icon>
      <h3 class="empty-title">No Suggestions</h3>
      <p class="empty-message">Check back later for user suggestions</p>
    </div>
  </div>

  <!-- Top Fashion Influencers -->
  <div class="influencers">
    <h3>Top Fashion Influencers</h3>

    <!-- Loading State -->
    <div *ngIf="isLoadingInfluencers" class="loading-container">
      <div class="loading-card" *ngFor="let item of [1,2,3]">
        <div class="loading-content">
          <div class="loading-line short"></div>
          <div class="loading-line medium"></div>
          <div class="loading-line long"></div>
        </div>
      </div>
    </div>

    <!-- Influencers Slider -->
    <div *ngIf="!isLoadingInfluencers && topInfluencers.length > 0" class="influencers-slider-container">
      <owl-carousel-o [options]="influencersCarouselOptions" class="influencers-slider">
        <ng-template carouselSlide *ngFor="let influencer of topInfluencers">
          <div class="influencer-item">
            <img [src]="influencer.avatar" [alt]="influencer.fullName">
            <div class="influencer-info">
              <h5>{{ influencer.username }}</h5>
              <p>{{ formatFollowerCount(influencer.followersCount) }} followers</p>
              <div class="influencer-stats">
                <span class="posts-count">{{ influencer.postsCount }} posts</span>
                <span class="engagement">{{ influencer.engagement }}% engagement</span>
              </div>
              <button class="follow-btn" (click)="followInfluencer(influencer.id)">
                {{ influencer.isFollowing ? 'Following' : 'Follow' }}
              </button>
            </div>
          </div>
        </ng-template>
      </owl-carousel-o>
    </div>

    <!-- Empty State -->
    <div *ngIf="!isLoadingInfluencers && topInfluencers.length === 0" class="empty-container">
      <ion-icon name="people-outline" class="empty-icon"></ion-icon>
      <h3 class="empty-title">No Influencers Found</h3>
      <p class="empty-message">Check back later for top fashion influencers</p>
    </div>
  </div>

  <!-- Shop by Category -->
  <div class="categories">
    <h3>Shop by Category</h3>
    <div class="categories-slider-container">
      <owl-carousel-o [options]="categoriesCarouselOptions" class="categories-slider">
        <ng-template carouselSlide *ngFor="let category of categories">
          <div
            class="category-item"
            [routerLink]="['/category', category.slug]"
            routerLinkActive="active"
            tabindex="0"
          >
            <img [src]="category.image" [alt]="category.name">
            <span>{{ category.name }}</span>
          </div>
        </ng-template>
      </owl-carousel-o>
    </div>
  </div>


</aside>
