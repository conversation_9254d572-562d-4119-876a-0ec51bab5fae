import { Component, Input, Output, EventEmitter, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { register } from 'swiper/element/bundle';

// Register Swiper custom elements
register();

export interface Story {
  _id: string;
  user: {
    _id: string;
    username: string;
    fullName: string;
    avatar?: string;
  };
  media: {
    type: 'image' | 'video';
    url: string;
  }[];
  viewed?: boolean;
  createdAt: Date;
}

export interface CurrentUser {
  _id: string;
  username: string;
  fullName: string;
  avatar?: string;
}

@Component({
  selector: 'app-stories-carousel',
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <div class="stories-carousel">
      <swiper-container
        class="stories-swiper"
        [slides-per-view]="slidesPerView"
        [space-between]="spaceBetween"
        free-mode="true"
        grab-cursor="true"
        mousewheel="true"
        keyboard="true"
        [breakpoints]="swiperBreakpoints">
        
        <!-- Add Story Slide -->
        <swiper-slide class="story-slide" *ngIf="showAddStory">
          <div class="story-item add-story" (click)="onCreateStory()">
            <div class="story-avatar add-avatar">
              <img [src]="currentUser?.avatar || defaultAvatar"
                   [alt]="currentUser?.fullName || 'Your Profile'"
                   class="profile-image"
                   (error)="onImageError($event)">
              <div class="add-story-plus">
                <i class="fas fa-plus"></i>
              </div>
            </div>
            <span class="story-username">{{ addStoryText }}</span>
          </div>
        </swiper-slide>

        <!-- Story Slides -->
        <swiper-slide 
          class="story-slide"
          *ngFor="let story of stories; let i = index">
          <div class="story-item" (click)="onStoryClick(story, i)">
            <div class="story-avatar" [class.viewed]="story.viewed">
              <img [src]="story.user.avatar || defaultAvatar"
                   [alt]="story.user.fullName"
                   (error)="onImageError($event)">
              <div class="story-ring" [class.viewed]="story.viewed"></div>
            </div>
            <span class="story-username">{{ story.user.username }}</span>
          </div>
        </swiper-slide>
      </swiper-container>
    </div>
  `,
  styles: [`
    .stories-carousel {
      width: 100%;
    }

    /* Swiper Carousel Styles */
    .stories-swiper {
      padding: 0 20px;
      overflow: visible;
    }

    .story-slide {
      width: auto !important;
      flex-shrink: 0;
    }

    .story-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      min-width: 70px;
      transition: transform 0.2s ease;
    }

    .story-item:hover {
      transform: translateY(-2px);
    }

    .story-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      padding: 2px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      transition: transform 0.2s ease;
      background: #fff;
    }

    .story-avatar:hover {
      transform: scale(1.05);
    }

    .story-ring {
      position: absolute;
      top: -3px;
      left: -3px;
      right: -3px;
      bottom: -3px;
      border-radius: 50%;
      background: linear-gradient(45deg, #ff6b6b, #ffa726, #ff6b6b);
      z-index: -1;
      animation: rotate 3s linear infinite;
    }

    .story-ring.viewed {
      background: #ddd;
      animation: none;
    }

    @keyframes rotate {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .story-avatar img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #fff;
    }

    .add-avatar {
      background: #fff;
      border: 2px solid #ddd;
      position: relative;
      overflow: hidden;
    }

    .add-avatar .profile-image {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      border: none;
    }

    .add-story-plus {
      position: absolute;
      bottom: -2px;
      right: -2px;
      width: 20px;
      height: 20px;
      background: #007bff;
      border: 2px solid #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 10px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .add-story-plus i {
      font-size: 10px;
      line-height: 1;
    }

    .story-username {
      font-size: 12px;
      color: #333;
      text-align: center;
      max-width: 70px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .stories-swiper {
        padding: 0 16px;
      }

      .story-avatar {
        width: 50px;
        height: 50px;
      }

      .story-ring {
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
      }

      .add-story-plus {
        width: 18px;
        height: 18px;
        font-size: 9px;
      }

      .story-username {
        font-size: 11px;
        max-width: 60px;
      }

      .story-item {
        min-width: 60px;
      }
    }

    @media (max-width: 480px) {
      .story-avatar {
        width: 45px;
        height: 45px;
      }

      .story-item {
        min-width: 55px;
        gap: 6px;
      }

      .add-story-plus {
        width: 16px;
        height: 16px;
        font-size: 8px;
      }
    }
  `]
})
export class StoriesCarouselComponent {
  @Input() stories: Story[] = [];
  @Input() showAddStory: boolean = true;
  @Input() addStoryText: string = 'Your Story';
  @Input() defaultAvatar: string = '/assets/images/default-avatar.png';
  @Input() slidesPerView: string = 'auto';
  @Input() spaceBetween: number = 16;
  @Input() currentUser: CurrentUser | null = null;

  @Output() storyClick = new EventEmitter<{story: Story, index: number}>();
  @Output() createStory = new EventEmitter<void>();

  swiperBreakpoints = {
    320: {
      slidesPerView: 'auto',
      spaceBetween: 12
    },
    768: {
      slidesPerView: 'auto',
      spaceBetween: 16
    }
  };

  onStoryClick(story: Story, index: number) {
    this.storyClick.emit({ story, index });
  }

  onCreateStory() {
    this.createStory.emit();
  }

  onImageError(event: any) {
    event.target.src = this.defaultAvatar;
  }
}
