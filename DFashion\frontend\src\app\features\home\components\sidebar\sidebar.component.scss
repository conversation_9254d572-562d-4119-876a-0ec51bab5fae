.sidebar {
  position: sticky;
  top: 80px;
  height: fit-content;
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  padding-right: 8px;
}

// Styling for the new components in sidebar
app-trending-products,
app-featured-brands,
app-new-arrivals {
  width: 100%;
  display: block;
  margin-bottom: 24px;
}

// Suggestions section with simple styling
.suggestions {
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;

  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .suggestion-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        background: #e9ecef;
        transform: translateY(-1px);
      }

      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
      }

      .suggestion-info {
        flex: 1;

        h5 {
          margin: 0 0 4px 0;
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }

        p {
          margin: 0;
          font-size: 12px;
          color: #666;
        }
      }

      .follow-btn {
        padding: 6px 12px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.2s ease;

        &:hover {
          background: #0056b3;
        }
      }
    }
  }
}

// Influencers section
.influencers {
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;

  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .influencer-list {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .influencer-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        background: #e9ecef;
        transform: translateY(-1px);
      }

      img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
      }

      .influencer-info {
        flex: 1;

        h5 {
          margin: 0 0 4px 0;
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }

        p {
          margin: 0 0 8px 0;
          font-size: 12px;
          color: #666;
        }

        .influencer-stats {
          display: flex;
          gap: 12px;
          margin-bottom: 8px;

          span {
            font-size: 11px;
            color: #888;
          }
        }

        .follow-btn {
          padding: 6px 12px;
          background: #007bff;
          color: white;
          border: none;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 500;
          cursor: pointer;
          transition: background 0.2s ease;

          &:hover {
            background: #0056b3;
          }
        }
      }
    }
  }
}

// Categories section
.categories {
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;

  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .category-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;

    .category-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      text-decoration: none;
      color: inherit;
      transition: all 0.2s ease;
      cursor: pointer;

      &:hover {
        background: #e9ecef;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      img {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        object-fit: cover;
        margin-bottom: 8px;
      }

      span {
        font-size: 12px;
        font-weight: 500;
        color: #333;
        text-align: center;
      }
    }
  }
}
