.trending-products-container {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  margin-bottom: 24px;
}

// Header Section
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .header-content {
    flex: 1;
  }
  
  .section-title {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
    
    .title-icon {
      font-size: 28px;
      color: #ff6b35;
    }
  }
  
  .section-subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
  
  .view-all-btn {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
    }
    
    ion-icon {
      font-size: 16px;
    }
  }
}

// Loading State
.loading-container {
  .loading-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }
  
  .loading-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    .loading-image {
      width: 100%;
      height: 200px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
    }
    
    .loading-content {
      padding: 16px;
      
      .loading-line {
        height: 12px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 6px;
        margin-bottom: 8px;
        
        &.short { width: 40%; }
        &.medium { width: 60%; }
        &.long { width: 80%; }
      }
    }
  }
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

// Error State
.error-container {
  text-align: center;
  padding: 40px 20px;
  
  .error-icon {
    font-size: 48px;
    color: #dc3545;
    margin-bottom: 16px;
  }
  
  .error-message {
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
  }
  
  .retry-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
    transition: background 0.3s ease;
    
    &:hover {
      background: #0056b3;
    }
  }
}

// Products Grid
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.product-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    
    .action-buttons {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.product-image-container {
  position: relative;
  overflow: hidden;
  
  .product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .trending-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
    
    ion-icon {
      font-size: 14px;
    }
  }
  
  .discount-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: #dc3545;
    color: white;
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 700;
  }
  
  .action-buttons {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    transition: all 0.3s ease;
    
    .action-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: none;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      
      ion-icon {
        font-size: 20px;
        color: #333;
      }
      
      &:hover {
        background: white;
        transform: scale(1.1);
      }
      
      &.like-btn {
        &:hover ion-icon {
          color: #dc3545;
        }

        &.liked {
          background: rgba(220, 53, 69, 0.1);

          ion-icon {
            color: #dc3545;
          }
        }
      }

      &.share-btn:hover ion-icon {
        color: #007bff;
      }
    }
  }
}

.product-info {
  padding: 16px;
  
  .product-brand {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
  }
  
  .product-name {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 12px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .price-section {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    
    .current-price {
      font-size: 18px;
      font-weight: 700;
      color: #ff6b35;
    }
    
    .original-price {
      font-size: 14px;
      color: #999;
      text-decoration: line-through;
    }
  }
  
  .rating-section {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    
    .stars {
      display: flex;
      gap: 2px;
      
      ion-icon {
        font-size: 14px;
        color: #ddd;
        
        &.filled {
          color: #ffc107;
        }
      }
    }
    
    .rating-text {
      font-size: 12px;
      color: #666;
    }
  }
  
  .product-actions {
    display: flex;
    gap: 8px;
    
    .cart-btn {
      flex: 1;
      background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
      color: white;
      border: none;
      padding: 12px 16px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
      }
      
      ion-icon {
        font-size: 16px;
      }
    }
    
    .wishlist-btn {
      width: 44px;
      height: 44px;
      border: 2px solid #e9ecef;
      background: white;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      
      ion-icon {
        font-size: 20px;
        color: #666;
      }
      
      &:hover {
        border-color: #ff6b35;
        
        ion-icon {
          color: #ff6b35;
        }
      }
    }
  }
}

// Empty State
.empty-container {
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 64px;
    color: #ccc;
    margin-bottom: 20px;
  }
  
  .empty-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }
  
  .empty-message {
    font-size: 14px;
    color: #666;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .trending-products-container {
    padding: 16px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    
    .view-all-btn {
      align-self: flex-end;
    }
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }
  
  .section-title {
    font-size: 20px;
  }
}
