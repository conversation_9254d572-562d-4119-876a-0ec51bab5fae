<div class="stories-container">
  <div class="stories-scroll-wrapper">
    <div class="stories-list">
      <!-- Add Story Item -->
      <div class="story-item add-story" *ngIf="showAddStory" (click)="onCreateStory()">
        <div class="story-avatar-container">
          <div class="story-avatar add-avatar">
            <img [src]="currentUser?.avatar || defaultAvatar"
                 [alt]="currentUser?.fullName || 'Your Profile'"
                 class="profile-image"
                 (error)="onImageError($event)">
          </div>
          <div class="add-story-plus">
            <i class="fas fa-plus"></i>
          </div>
        </div>
        <span class="story-username">{{ addStoryText }}</span>
      </div>

      <!-- Story Items -->
      <div class="story-item"
           *ngFor="let story of stories; let i = index"
           (click)="onStoryClick(story, i)">
        <div class="story-avatar-container">
          <div class="story-avatar" [class.viewed]="story.viewed">
            <div class="story-ring" [class.viewed]="story.viewed"></div>
            <img [src]="story.user.avatar || defaultAvatar"
                 [alt]="story.user.fullName"
                 class="profile-image"
                 (error)="onImageError($event)">
          </div>
        </div>
        <span class="story-username">{{ story.user.username }}</span>
      </div>
    </div>
  </div>
</div>
