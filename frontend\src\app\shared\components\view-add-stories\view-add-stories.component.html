<div class="stories-container">
  <swiper-container
    class="stories-swiper"
    [slides-per-view]="slidesPerView"
    [space-between]="spaceBetween"
    free-mode="true"
    grab-cursor="true"
    mousewheel="true"
    keyboard="true"
    [breakpoints]="swiperBreakpoints"
    loop="false"
    centered-slides="false">
    
    <!-- Add Story Slide -->
    <swiper-slide class="story-slide" *ngIf="showAddStory">
      <div class="story-item add-story" (click)="onCreateStory()">
        <div class="story-avatar-container">
          <div class="story-avatar add-avatar">
            <img [src]="currentUser?.avatar || defaultAvatar"
                 [alt]="currentUser?.fullName || 'Your Profile'"
                 class="profile-image"
                 (error)="onImageError($event)">
          </div>
          <div class="add-story-plus">
            <i class="fas fa-plus"></i>
          </div>
        </div>
        <span class="story-username">{{ addStoryText }}</span>
      </div>
    </swiper-slide>

    <!-- Story Slides -->
    <swiper-slide 
      class="story-slide"
      *ngFor="let story of stories; let i = index">
      <div class="story-item" (click)="onStoryClick(story, i)">
        <div class="story-avatar-container">
          <div class="story-avatar" [class.viewed]="story.viewed">
            <div class="story-ring" [class.viewed]="story.viewed"></div>
            <img [src]="story.user.avatar || defaultAvatar"
                 [alt]="story.user.fullName"
                 class="profile-image"
                 (error)="onImageError($event)">
          </div>
        </div>
        <span class="story-username">{{ story.user.username }}</span>
      </div>
    </swiper-slide>
  </swiper-container>
</div>
