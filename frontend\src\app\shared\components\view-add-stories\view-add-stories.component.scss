.stories-container {
  width: 100%;
  padding: 20px 0;
  background: #fff;
  border-bottom: 1px solid #dbdbdb;
}

.stories-swiper {
  width: 100%;
  height: auto;
  padding: 0 20px;
}

.story-slide {
  width: auto;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
}

.story-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
  width: 80px;
  max-width: 80px;
  flex-shrink: 0;
}

.story-item:hover {
  transform: scale(1.05);
}

.story-avatar-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.story-avatar {
  width: 66px;
  height: 66px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}

.story-ring {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  padding: 3px;
  z-index: 1;
}

.story-ring.viewed {
  background: #c7c7c7;
}

.story-avatar .profile-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #fff;
  position: relative;
  z-index: 2;
}

/* Add Story Specific Styles */
.add-story .story-avatar-container {
  position: relative;
}

.add-story .story-avatar {
  width: 66px;
  height: 66px;
  border: 3px solid #dbdbdb;
  background: #fff;
}

.add-story .profile-image {
  width: 60px;
  height: 60px;
  border: none;
}

.add-story-plus {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 24px;
  height: 24px;
  background: #0095f6;
  border: 3px solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  z-index: 3;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.add-story-plus i {
  font-size: 12px;
  line-height: 1;
}

.story-username {
  font-size: 12px;
  color: #262626;
  text-align: center;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 400;
}

/* Responsive */
@media (max-width: 768px) {
  .stories-swiper {
    padding: 0 16px;
  }

  .story-avatar {
    width: 56px;
    height: 56px;
  }

  .story-avatar .profile-image {
    width: 50px;
    height: 50px;
  }

  .add-story .story-avatar {
    width: 56px;
    height: 56px;
  }

  .add-story .profile-image {
    width: 50px;
    height: 50px;
  }

  .add-story-plus {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }

  .story-username {
    font-size: 11px;
    max-width: 70px;
  }

  .story-item {
    width: 70px;
    max-width: 70px;
  }
}

@media (max-width: 480px) {
  .story-avatar {
    width: 50px;
    height: 50px;
  }

  .story-avatar .profile-image {
    width: 44px;
    height: 44px;
  }

  .add-story .story-avatar {
    width: 50px;
    height: 50px;
  }

  .add-story .profile-image {
    width: 44px;
    height: 44px;
  }

  .story-item {
    width: 60px;
    max-width: 60px;
    gap: 6px;
  }

  .add-story-plus {
    width: 18px;
    height: 18px;
    font-size: 9px;
  }

  .story-username {
    font-size: 10px;
    max-width: 60px;
  }

  .stories-swiper {
    padding: 0 12px;
  }
}
