// Instagram-style Stories Container
.stories-container {
  background: white;
  border-bottom: 1px solid #dbdbdb;
  padding: 16px 0;
  margin-bottom: 0;
}

.stories-slider {
  display: flex;
  gap: 16px;
  padding: 0 16px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.story-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  flex-shrink: 0;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.story-avatar-container {
  position: relative;
  margin-bottom: 8px;
}

.story-avatar {
  width: 66px;
  height: 66px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  border: 2px solid white;
  position: relative;
  z-index: 2;
}

.story-ring {
  position: absolute;
  top: -2px;
  left: -2px;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  z-index: 1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.story-username {
  font-size: 12px;
  color: #262626;
  font-weight: 400;
  max-width: 74px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Add Story Button Styles
.add-story-item {
  .story-username {
    font-weight: 600;
    color: #262626;
  }
}

.add-story-avatar {
  width: 66px;
  height: 66px;
  border-radius: 50%;
  position: relative;
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.add-story-icon {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 20px;
  height: 20px;
  background: #0095f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  z-index: 3;

  i {
    color: white;
    font-size: 10px;
    font-weight: bold;
  }
}

.current-user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  border: 2px solid white;
}

// Loading State for Stories
.stories-loading {
  display: flex;
  gap: 16px;
  padding: 0 16px;
}

.story-skeleton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.skeleton-avatar {
  width: 66px;
  height: 66px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-name {
  width: 60px;
  height: 12px;
  border-radius: 6px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.story-bar__user {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
  
  &.bounce {
    animation: bounce 0.3s ease;
  }
}

.story-bar__user-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  border: 3px solid transparent;
  background-clip: padding-box;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    z-index: -1;
  }
}

.story-bar__user-name {
  margin-top: 4px;
  font-size: 12px;
  color: #262626;
  text-align: center;
  max-width: 64px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Stories Viewer
.stories-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  z-index: 9999;
  perspective: 400px;
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  
  &.is-open {
    opacity: 1;
    visibility: visible;
  }
}

.stories {
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transform: translateZ(-50vw);
  transition: transform 0.25s ease-out;
  
  &.is-closed {
    opacity: 0;
    transform: scale(0.1);
  }
}

// Story Progress Bars
.story-progress {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  gap: 2px;
  z-index: 100;
}

.story-progress__bar {
  flex: 1;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 1px;
  overflow: hidden;
  
  &.completed .story-progress__fill {
    width: 100%;
  }
  
  &.active .story-progress__fill {
    animation: progress 15s linear;
  }
}

.story-progress__fill {
  height: 100%;
  background: #fff;
  width: 0%;
  transition: width 0.1s ease;
}

@keyframes progress {
  from { width: 0%; }
  to { width: 100%; }
}

// Individual Story
.story {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  user-select: none;
}

.story__top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 48px 16px 16px;
  background: linear-gradient(180deg, rgba(0,0,0,0.6) 0%, transparent 100%);
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.story__details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.story__avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  border: 2px solid #fff;
}

.story__user {
  color: #fff;
  font-weight: 600;
  font-size: 14px;
}

.story__time {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.story__views {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  margin-left: 8px;
}

.story__close {
  background: none;
  border: none;
  color: #fff;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

// Story Content
.story__content {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

.story__video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.story__image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

// Story Caption
.story__caption {
  position: absolute;
  bottom: 120px;
  left: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 12px 16px;
  border-radius: 20px;
  font-size: 14px;
  line-height: 1.4;
  backdrop-filter: blur(10px);
  z-index: 5;
}

// Product Tags
.story__product-tags {
  position: absolute;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  z-index: 6;
}

.product-tag {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 8px 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 160px;

  &:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
}

.product-tag-icon {
  font-size: 16px;
}

.product-tag-info {
  flex: 1;
}

.product-tag-name {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
  line-height: 1.2;
}

.product-tag-price {
  font-size: 11px;
  color: #666;
  font-weight: 500;
}

// Story Bottom Actions
.story__bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: linear-gradient(0deg, rgba(0,0,0,0.6) 0%, transparent 100%);
  z-index: 10;
}

.story__actions {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.story__action-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
  }
}

// E-commerce Actions
.story__ecommerce-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.ecommerce-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &.buy-now-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: #fff;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
    }
  }

  &.wishlist-btn {
    background: linear-gradient(45deg, #ff9ff3, #f368e0);
    color: #fff;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 159, 243, 0.4);
    }
  }

  &.cart-btn {
    background: linear-gradient(45deg, #54a0ff, #2e86de);
    color: #fff;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(84, 160, 255, 0.4);
    }
  }
}

// Navigation Areas (Invisible)
.story__nav-area {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 33%;
  z-index: 5;
  cursor: pointer;

  &.story__nav-prev {
    left: 0;
  }

  &.story__nav-next {
    right: 0;
    width: 67%;
  }
}

// Feed Cover
.feed__cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: -1;

  &.is-hidden {
    opacity: 0;
  }
}

// Touch Indicators (Mobile)
.touch-indicators {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: 101;
  pointer-events: none;
  display: none;

  @media (max-width: 768px) {
    display: block;
  }
}

.touch-indicator {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  animation: fadeInOut 3s infinite;

  &.left {
    left: 16px;
  }

  &.right {
    right: 16px;
  }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

@keyframes bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(0.8); }
}

// Enhanced Mobile Optimization
@media (max-width: 1024px) {
  .story-bar {
    padding: 12px 16px;
    gap: 10px;
    overflow-x: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  .stories-wrapper {
    touch-action: pan-y;
  }

  .story {
    touch-action: manipulation;
  }
}

@media (max-width: 768px) {
  .story-bar {
    padding: 8px 12px;
    gap: 8px;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .story-bar__user-avatar {
    width: 48px;
    height: 48px;

    &::before {
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
    }
  }

  .story-bar__user-name {
    font-size: 11px;
    max-width: 56px;
  }

  .story__top {
    padding: 40px 12px 12px;
  }

  .story__bottom {
    padding: 12px;
  }

  .story__ecommerce-actions {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 6px;
    justify-content: space-between;
  }

  .ecommerce-btn {
    padding: 8px 12px;
    font-size: 11px;
    flex: 1;
    min-width: 80px;

    i {
      font-size: 12px;
    }
  }

  .story__actions {
    gap: 12px;
    margin-bottom: 8px;
  }

  .story__action-btn {
    font-size: 18px;
    padding: 6px;
  }
}

@media (max-width: 480px) {
  .story-bar {
    padding: 6px 8px;
    gap: 6px;
  }

  .story-bar__user-avatar {
    width: 40px;
    height: 40px;

    &::before {
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
    }
  }

  .story-bar__user-name {
    font-size: 10px;
    max-width: 48px;
  }

  .story__top {
    padding: 32px 8px 8px;
  }

  .story__bottom {
    padding: 8px;
  }

  .story__ecommerce-actions {
    flex-direction: column;
    gap: 4px;
  }

  .ecommerce-btn {
    padding: 6px 8px;
    font-size: 10px;

    i {
      font-size: 10px;
    }
  }

  .story__actions {
    gap: 8px;
    margin-bottom: 6px;
  }

  .story__action-btn {
    font-size: 16px;
    padding: 4px;
  }

  .story__user {
    font-size: 12px;
  }

  .story__time {
    font-size: 10px;
  }

  .story__avatar {
    width: 28px;
    height: 28px;
  }
}

// Enhanced Touch Interactions
@media (hover: none) and (pointer: coarse) {
  .story-bar__user {
    &:active {
      transform: scale(0.95);
      transition: transform 0.1s ease;
    }
  }

  .ecommerce-btn {
    &:active {
      transform: scale(0.95);
      transition: transform 0.1s ease;
    }
  }

  .story__action-btn {
    &:active {
      transform: scale(0.9);
      transition: transform 0.1s ease;
    }
  }

  .story__close {
    &:active {
      transform: scale(0.9);
      transition: transform 0.1s ease;
    }
  }
}

// Landscape Mobile Optimization
@media (max-width: 896px) and (orientation: landscape) {
  .story__top {
    padding: 24px 12px 8px;
  }

  .story__bottom {
    padding: 8px 12px;
  }

  .story__ecommerce-actions {
    flex-direction: row;
    gap: 8px;
  }

  .ecommerce-btn {
    padding: 6px 10px;
    font-size: 10px;
  }
}

/* Middle Navigation Button */
.middle-navigation {
  position: absolute;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 4;

  .middle-nav-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
    }

    &:active {
      transform: translateY(0);
    }

    i {
      font-size: 16px;
    }

    span {
      font-size: 14px;
    }
  }
}

// High DPI Displays
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .story-bar__user-avatar {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  .story__avatar {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

// Mobile responsive for middle navigation
@media (max-width: 768px) {
  .middle-navigation {
    bottom: 80px;

    .middle-nav-btn {
      padding: 10px 20px;
      font-size: 12px;

      i {
        font-size: 14px;
      }

      span {
        font-size: 12px;
      }
    }
  }
}
