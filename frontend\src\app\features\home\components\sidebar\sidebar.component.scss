.sidebar {
  position: sticky;
  top: 80px;
  height: fit-content;
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  padding-right: 8px;
}

// Styling for the new components in sidebar
app-view-add-stories,
app-trending-products,
app-featured-brands,
app-new-arrivals {
  width: 100%;
  display: block;
  margin-bottom: 24px;
}

.suggestions,
.trending,
.categories {
  background: #fff;
  border: 1px solid #dbdbdb;
  border-radius: 8px;
  padding: 16px;
}

// Influencers section with new-arrivals-container styling
.influencers {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  margin-bottom: 24px;
}

.suggestions h3,
.trending h3,
.categories h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #8e8e8e;
}

// Influencers h3 with new-arrivals styling
.influencers h3 {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin: 0 0 24px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.influencers h3::before {
  content: '👑';
  font-size: 28px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

// Influencer items with new-arrivals card styling
.influencer-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.influencer-item:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.influencer-item:last-child {
  margin-bottom: 0;
}

.suggestion-item img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.suggestion-info {
  flex: 1;
}

.suggestion-info h5 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
}

.suggestion-info p {
  font-size: 12px;
  color: #8e8e8e;
}

// Influencer styling with new-arrivals pattern
.influencer-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.influencer-item img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease;
}

.influencer-item:hover img {
  transform: scale(1.1);
}

.influencer-info {
  flex: 1;
}

.influencer-info h5 {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.influencer-info p {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
}

.influencer-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.influencer-stats span {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
  width: fit-content;
  backdrop-filter: blur(5px);
}

.follow-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  align-self: flex-start;
}

.follow-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

// Loading and Empty States for Influencers
.influencers .loading-container {
  .loading-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);

    .loading-content {
      .loading-line {
        height: 12px;
        background: rgba(255, 255, 255, 0.2);
        animation: loading 1.5s infinite;
        border-radius: 6px;
        margin-bottom: 8px;

        &.short { width: 40%; }
        &.medium { width: 60%; }
        &.long { width: 80%; }
      }
    }
  }
}

@keyframes loading {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.influencers .empty-container {
  text-align: center;
  padding: 40px 20px;

  .empty-icon {
    font-size: 48px;
    color: rgba(255, 255, 255, 0.4);
    margin-bottom: 16px;
  }

  .empty-title {
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
  }

  .empty-message {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }
}

.trending-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;
}

.trending-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.trending-item img {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.trending-info {
  flex: 1;
}

.trending-info h5 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.trending-info p {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.original-price {
  text-decoration: line-through;
  color: #8e8e8e;
  font-weight: 400;
  margin-left: 4px;
}

.trending-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.trending-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
  background: #fef3c7;
  color: #92400e;
  width: fit-content;
}

.views {
  font-size: 11px;
  color: #8e8e8e;
}

.quick-buy-btn {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  align-self: flex-start;
}

.quick-buy-btn:hover {
  background: var(--primary-color);
  color: #fff;
  border-color: var(--primary-color);
}

.categories {
  margin-bottom: 24px;
}

.categories h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.categories h3::before {
  content: '🛍️';
  font-size: 18px;
}

.category-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  position: relative;
  overflow: hidden;
}

.category-item:hover {
  background: #f8fafc;
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-item.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.category-item.active span {
  color: white;
}

.category-item img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
  margin-bottom: 8px;
  transition: transform 0.3s ease;
}

.category-item:hover img {
  transform: scale(1.1);
}

.category-item span {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  transition: color 0.3s ease;
}

.category-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.category-item:hover::before {
  left: 100%;
}

@media (max-width: 1024px) {
  .sidebar {
    order: -1;
    position: static;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    max-height: none;
    overflow-y: visible;
  }

  // Adjust component layout for tablet view
  app-view-add-stories,
  app-trending-products,
  app-featured-brands,
  app-new-arrivals {
    margin-bottom: 16px;
  }

  // Responsive influencers section
  .influencers {
    padding: 16px;
  }

  .influencer-item {
    padding: 12px;
  }

  .influencer-item img {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    grid-template-columns: 1fr;
  }

  // Stack components vertically on mobile
  app-view-add-stories,
  app-trending-products,
  app-featured-brands,
  app-new-arrivals {
    margin-bottom: 12px;
  }

  // Mobile influencers section
  .influencers {
    padding: 12px;
  }

  .influencers h3 {
    font-size: 20px;
  }

  .influencer-item {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }

  .influencer-item img {
    width: 60px;
    height: 60px;
    margin-bottom: 12px;
  }

  .influencer-stats {
    flex-direction: row;
    justify-content: center;
    gap: 8px;
  }
}

// Custom scrollbar for sidebar
.sidebar {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
