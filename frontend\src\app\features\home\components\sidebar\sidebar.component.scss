.sidebar {
  position: sticky;
  top: 80px;
  height: fit-content;
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  padding-right: 8px;
}

// Styling for the new components in sidebar
app-view-add-stories,
app-trending-products,
app-featured-brands,
app-new-arrivals {
  width: 100%;
  display: block;
  margin-bottom: 24px;
}

// Suggestions section with modern styling
.suggestions {
  padding: 20px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border-radius: 16px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.2);
}

// Categories section with modern styling
.categories {
  padding: 20px;
  background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);
  border-radius: 16px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 210, 211, 0.2);
}

.trending {
  background: #fff;
  border: 1px solid #dbdbdb;
  border-radius: 8px;
  padding: 16px;
}

// Influencers section with new-arrivals-container styling
.influencers {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  margin-bottom: 24px;
}

// Suggestions header styling
.suggestions h3 {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin: 0 0 24px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.suggestions h3::before {
  content: '💡';
  font-size: 28px;
}

// Categories header styling
.categories h3 {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin: 0 0 24px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.categories h3::before {
  content: '🛍️';
  font-size: 28px;
}

.trending h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #8e8e8e;
}

// Influencers h3 with new-arrivals styling
.influencers h3 {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin: 0 0 24px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.influencers h3::before {
  content: '👑';
  font-size: 28px;
}

// Suggestion items with modern card styling
.suggestion-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 16px;
}

.suggestion-item:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.suggestion-item:last-child {
  margin-bottom: 0;
}

// Influencer items with new-arrivals card styling
.influencer-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.influencer-item:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.influencer-item:last-child {
  margin-bottom: 0;
}

// Suggestion item content styling
.suggestion-item img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease;
}

.suggestion-item:hover img {
  transform: scale(1.1);
}

.suggestion-info {
  flex: 1;
}

.suggestion-info h5 {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.suggestion-info p {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

// Follow button styling for suggestions
.suggestions .follow-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.suggestions .follow-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

// Loading and Empty States for Suggestions
.suggestions .loading-container {
  .loading-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);

    .loading-content {
      .loading-line {
        height: 12px;
        background: rgba(255, 255, 255, 0.2);
        animation: loading 1.5s infinite;
        border-radius: 6px;
        margin-bottom: 8px;

        &.short { width: 40%; }
        &.medium { width: 60%; }
        &.long { width: 80%; }
      }
    }
  }
}

.suggestions .empty-container {
  text-align: center;
  padding: 40px 20px;

  .empty-icon {
    font-size: 48px;
    color: rgba(255, 255, 255, 0.4);
    margin-bottom: 16px;
  }

  .empty-title {
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
  }

  .empty-message {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }
}

// Influencer styling with new-arrivals pattern
.influencer-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.influencer-item img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease;
}

.influencer-item:hover img {
  transform: scale(1.1);
}

.influencer-info {
  flex: 1;
}

.influencer-info h5 {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.influencer-info p {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
}

.influencer-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.influencer-stats span {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
  width: fit-content;
  backdrop-filter: blur(5px);
}

.follow-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  align-self: flex-start;
}

.follow-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

// Loading and Empty States for Influencers
.influencers .loading-container {
  .loading-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);

    .loading-content {
      .loading-line {
        height: 12px;
        background: rgba(255, 255, 255, 0.2);
        animation: loading 1.5s infinite;
        border-radius: 6px;
        margin-bottom: 8px;

        &.short { width: 40%; }
        &.medium { width: 60%; }
        &.long { width: 80%; }
      }
    }
  }
}

@keyframes loading {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.influencers .empty-container {
  text-align: center;
  padding: 40px 20px;

  .empty-icon {
    font-size: 48px;
    color: rgba(255, 255, 255, 0.4);
    margin-bottom: 16px;
  }

  .empty-title {
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
  }

  .empty-message {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }
}

.trending-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;
}

.trending-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.trending-item img {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.trending-info {
  flex: 1;
}

.trending-info h5 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.trending-info p {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.original-price {
  text-decoration: line-through;
  color: #8e8e8e;
  font-weight: 400;
  margin-left: 4px;
}

.trending-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.trending-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
  background: #fef3c7;
  color: #92400e;
  width: fit-content;
}

.views {
  font-size: 11px;
  color: #8e8e8e;
}

.quick-buy-btn {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  align-self: flex-start;
}

.quick-buy-btn:hover {
  background: var(--primary-color);
  color: #fff;
  border-color: var(--primary-color);
}

.categories {
  margin-bottom: 24px;
}

.categories h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.categories h3::before {
  content: '🛍️';
  font-size: 18px;
}

// Enhanced category grid styling
.category-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.category-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: white;
  position: relative;
  overflow: hidden;
}

.category-item:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.category-item.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-4px);
}

.category-item.active span {
  color: white;
}

.category-item img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease;
}

.category-item:hover img {
  transform: scale(1.1);
}

.category-item span {
  font-size: 14px;
  font-weight: 600;
  color: white;
  text-align: center;
  position: relative;
  z-index: 1;
}

.category-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-item:hover::before {
  opacity: 1;
}

@media (max-width: 1024px) {
  .sidebar {
    order: -1;
    position: static;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    max-height: none;
    overflow-y: visible;
  }

  // Adjust component layout for tablet view
  app-view-add-stories,
  app-trending-products,
  app-featured-brands,
  app-new-arrivals {
    margin-bottom: 16px;
  }

  // Responsive sections
  .suggestions,
  .influencers,
  .categories {
    padding: 16px;
  }

  .suggestion-item,
  .influencer-item {
    padding: 12px;
  }

  .suggestion-item img,
  .influencer-item img {
    width: 50px;
    height: 50px;
  }

  .category-grid {
    gap: 12px;
  }

  .category-item {
    padding: 16px 12px;
  }

  .category-item img {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    grid-template-columns: 1fr;
  }

  // Stack components vertically on mobile
  app-view-add-stories,
  app-trending-products,
  app-featured-brands,
  app-new-arrivals {
    margin-bottom: 12px;
  }

  // Mobile sections
  .suggestions,
  .influencers,
  .categories {
    padding: 12px;
  }

  .suggestions h3,
  .influencers h3,
  .categories h3 {
    font-size: 20px;
  }

  .suggestion-item,
  .influencer-item {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }

  .suggestion-item img,
  .influencer-item img {
    width: 60px;
    height: 60px;
    margin-bottom: 12px;
  }

  .influencer-stats {
    flex-direction: row;
    justify-content: center;
    gap: 8px;
  }

  .category-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .category-item {
    flex-direction: row;
    text-align: left;
    padding: 12px;
    gap: 12px;
  }

  .category-item img {
    width: 40px;
    height: 40px;
    margin-bottom: 0;
  }
}

// Custom scrollbar for sidebar
.sidebar {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
