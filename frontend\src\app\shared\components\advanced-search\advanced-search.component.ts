import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Subject, Observable, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap, takeUntil, startWith } from 'rxjs/operators';
import { SearchService, SearchSuggestion, SearchFilters, TrendingSearch } from '../../../core/services/search.service';
import { ProductService } from '../../../core/services/product.service';

@Component({
  selector: 'app-advanced-search',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule],
  template: `
    <div class="advanced-search-container">
      <!-- Search Input with Suggestions -->
      <div class="search-input-container">
        <div class="search-bar">
          <ion-searchbar
            #searchInput
            [(ngModel)]="searchQuery"
            (ionInput)="onSearchInput($event)"
            (ionFocus)="onSearchFocus()"
            (ionBlur)="onSearchBlur()"
            (keydown.enter)="performSearch()"
            [placeholder]="placeholder"
            [showClearButton]="'focus'"
            [debounce]="0"
            class="custom-searchbar">
          </ion-searchbar>
          
          <!-- Voice Search Button -->
          <ion-button 
            *ngIf="enableVoiceSearch"
            fill="clear" 
            size="small"
            (click)="startVoiceSearch()"
            class="voice-search-btn">
            <ion-icon name="mic" slot="icon-only"></ion-icon>
          </ion-button>
        </div>

        <!-- Search Suggestions Dropdown -->
        <div 
          *ngIf="showSuggestions && (suggestions.length > 0 || trendingSearches.length > 0)"
          class="suggestions-dropdown">
          
          <!-- Search Suggestions -->
          <div *ngIf="suggestions.length > 0" class="suggestions-section">
            <div class="section-header">
              <ion-icon name="search"></ion-icon>
              <span>Suggestions</span>
            </div>
            <ion-item 
              *ngFor="let suggestion of suggestions; trackBy: trackSuggestion"
              button
              (click)="selectSuggestion(suggestion)"
              class="suggestion-item">
              <ion-icon 
                [name]="getSuggestionIcon(suggestion.type)" 
                slot="start"
                class="suggestion-icon">
              </ion-icon>
              <ion-label>
                <h3 [innerHTML]="highlightQuery(suggestion.text)"></h3>
                <p class="suggestion-type">{{ getSuggestionTypeLabel(suggestion.type) }}</p>
              </ion-label>
              <ion-badge 
                *ngIf="suggestion.popularity > 0"
                color="medium"
                slot="end">
                {{ suggestion.popularity }}
              </ion-badge>
            </ion-item>
          </div>

          <!-- Trending Searches -->
          <div *ngIf="trendingSearches.length > 0 && !searchQuery" class="suggestions-section">
            <div class="section-header">
              <ion-icon name="trending-up"></ion-icon>
              <span>Trending</span>
            </div>
            <ion-item 
              *ngFor="let trending of trendingSearches; trackBy: trackTrending"
              button
              (click)="selectTrendingSearch(trending)"
              class="suggestion-item trending-item">
              <ion-icon name="flame" slot="start" color="danger"></ion-icon>
              <ion-label>
                <h3>{{ trending.query }}</h3>
                <p>{{ trending.searches }} searches</p>
              </ion-label>
              <ion-chip 
                *ngIf="trending.growth && trending.growth > 0"
                color="success"
                slot="end">
                +{{ trending.growth }}
              </ion-chip>
            </ion-item>
          </div>

          <!-- Recent Searches -->
          <div *ngIf="recentSearches.length > 0 && !searchQuery" class="suggestions-section">
            <div class="section-header">
              <ion-icon name="time"></ion-icon>
              <span>Recent</span>
              <ion-button 
                fill="clear" 
                size="small"
                (click)="clearRecentSearches()"
                slot="end">
                Clear
              </ion-button>
            </div>
            <ion-item 
              *ngFor="let recent of recentSearches; trackBy: trackRecent"
              button
              (click)="selectRecentSearch(recent)"
              class="suggestion-item recent-item">
              <ion-icon name="time-outline" slot="start"></ion-icon>
              <ion-label>
                <h3>{{ recent.query }}</h3>
                <p>{{ recent.resultsCount }} results • {{ getRelativeTime(recent.timestamp) }}</p>
              </ion-label>
            </ion-item>
          </div>
        </div>
      </div>

      <!-- Advanced Filters -->
      <div *ngIf="showFilters" class="filters-container">
        <form [formGroup]="filtersForm" class="filters-form">
          
          <!-- Category Filter -->
          <div class="filter-group">
            <ion-select 
              formControlName="category"
              placeholder="Category"
              interface="popover"
              (ionChange)="onFilterChange()">
              <ion-select-option value="">All Categories</ion-select-option>
              <ion-select-option 
                *ngFor="let category of categories" 
                [value]="category.value">
                {{ category.label }}
              </ion-select-option>
            </ion-select>
          </div>

          <!-- Brand Filter -->
          <div class="filter-group">
            <ion-select 
              formControlName="brand"
              placeholder="Brand"
              interface="popover"
              (ionChange)="onFilterChange()">
              <ion-select-option value="">All Brands</ion-select-option>
              <ion-select-option 
                *ngFor="let brand of brands" 
                [value]="brand">
                {{ brand }}
              </ion-select-option>
            </ion-select>
          </div>

          <!-- Price Range -->
          <div class="filter-group price-range">
            <ion-label>Price Range</ion-label>
            <div class="price-inputs">
              <ion-input 
                formControlName="minPrice"
                type="number"
                placeholder="Min"
                (ionBlur)="onFilterChange()">
              </ion-input>
              <span class="price-separator">-</span>
              <ion-input 
                formControlName="maxPrice"
                type="number"
                placeholder="Max"
                (ionBlur)="onFilterChange()">
              </ion-input>
            </div>
          </div>

          <!-- Rating Filter -->
          <div class="filter-group">
            <ion-select 
              formControlName="rating"
              placeholder="Rating"
              interface="popover"
              (ionChange)="onFilterChange()">
              <ion-select-option value="">Any Rating</ion-select-option>
              <ion-select-option value="4">4+ Stars</ion-select-option>
              <ion-select-option value="3">3+ Stars</ion-select-option>
              <ion-select-option value="2">2+ Stars</ion-select-option>
            </ion-select>
          </div>

          <!-- Additional Filters -->
          <div class="filter-group checkbox-filters">
            <ion-checkbox 
              formControlName="inStock"
              (ionChange)="onFilterChange()">
            </ion-checkbox>
            <ion-label>In Stock Only</ion-label>
          </div>

          <div class="filter-group checkbox-filters">
            <ion-checkbox 
              formControlName="onSale"
              (ionChange)="onFilterChange()">
            </ion-checkbox>
            <ion-label>On Sale</ion-label>
          </div>

          <!-- Sort Options -->
          <div class="filter-group">
            <ion-select 
              formControlName="sortBy"
              placeholder="Sort By"
              interface="popover"
              (ionChange)="onFilterChange()">
              <ion-select-option value="relevance">Relevance</ion-select-option>
              <ion-select-option value="price">Price</ion-select-option>
              <ion-select-option value="rating">Rating</ion-select-option>
              <ion-select-option value="popularity">Popularity</ion-select-option>
              <ion-select-option value="newest">Newest</ion-select-option>
              <ion-select-option value="name">Name</ion-select-option>
            </ion-select>
          </div>

          <!-- Clear Filters Button -->
          <ion-button 
            fill="clear" 
            size="small"
            (click)="clearFilters()"
            class="clear-filters-btn">
            <ion-icon name="close-circle" slot="start"></ion-icon>
            Clear Filters
          </ion-button>
        </form>
      </div>

      <!-- Active Filters Display -->
      <div *ngIf="activeFilters.length > 0" class="active-filters">
        <ion-chip 
          *ngFor="let filter of activeFilters; trackBy: trackFilter"
          (click)="removeFilter(filter)"
          class="filter-chip">
          <ion-label>{{ filter.label }}: {{ filter.value }}</ion-label>
          <ion-icon name="close-circle"></ion-icon>
        </ion-chip>
      </div>

      <!-- Search Results Summary -->
      <div *ngIf="searchResults" class="search-summary">
        <p>
          <strong>{{ searchResults.pagination.total }}</strong> results found
          <span *ngIf="searchQuery"> for "{{ searchQuery }}"</span>
          <span *ngIf="searchResults.searchMeta.searchTime"> 
            ({{ getSearchTime(searchResults.searchMeta.searchTime) }}ms)
          </span>
        </p>
      </div>
    </div>
  `,
  styleUrls: ['./advanced-search.component.scss']
})
export class AdvancedSearchComponent implements OnInit, OnDestroy {
  @Input() placeholder: string = 'Search for products, brands, and more...';
  @Input() showFilters: boolean = true;
  @Input() enableVoiceSearch: boolean = true;
  @Input() autoFocus: boolean = false;
  
  @Output() searchPerformed = new EventEmitter<{ query: string; filters: SearchFilters }>();
  @Output() suggestionSelected = new EventEmitter<SearchSuggestion>();
  @Output() filtersChanged = new EventEmitter<SearchFilters>();

  @ViewChild('searchInput', { static: false }) searchInputRef!: ElementRef;

  // Form and state
  filtersForm: FormGroup;
  searchQuery: string = '';
  showSuggestions: boolean = false;
  
  // Data
  suggestions: SearchSuggestion[] = [];
  trendingSearches: TrendingSearch[] = [];
  recentSearches: any[] = [];
  categories: any[] = [];
  brands: string[] = [];
  activeFilters: any[] = [];
  searchResults: any = null;

  // Subjects for cleanup
  private destroy$ = new Subject<void>();
  private searchSubject = new Subject<string>();

  constructor(
    private fb: FormBuilder,
    private searchService: SearchService,
    private productService: ProductService
  ) {
    this.filtersForm = this.createFiltersForm();
  }

  ngOnInit(): void {
    this.initializeComponent();
    this.setupSearchSubscriptions();
    this.loadInitialData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createFiltersForm(): FormGroup {
    return this.fb.group({
      category: [''],
      brand: [''],
      minPrice: [''],
      maxPrice: [''],
      rating: [''],
      inStock: [false],
      onSale: [false],
      sortBy: ['relevance']
    });
  }

  private initializeComponent(): void {
    // Subscribe to search service state
    this.searchService.searchQuery$
      .pipe(takeUntil(this.destroy$))
      .subscribe(query => {
        if (query !== this.searchQuery) {
          this.searchQuery = query;
        }
      });

    this.searchService.searchResults$
      .pipe(takeUntil(this.destroy$))
      .subscribe(results => {
        this.searchResults = results;
        this.updateActiveFilters();
      });
  }

  private setupSearchSubscriptions(): void {
    // Setup debounced search for suggestions
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(query => {
        if (query.length > 1) {
          return this.searchService.getSearchSuggestions(query, 8);
        }
        return of([]);
      }),
      takeUntil(this.destroy$)
    ).subscribe(suggestions => {
      this.suggestions = suggestions;
    });
  }

  private loadInitialData(): void {
    // Load categories
    this.productService.getCategories().subscribe(response => {
      if (response.success) {
        this.categories = response.data.map((cat: any) => ({
          value: cat.name || cat,
          label: cat.displayName || cat.name || cat
        }));
      }
    });

    // Load brands
    this.productService.getBrands().subscribe(response => {
      this.brands = response.brands || [];
    });

    // Load trending searches
    this.searchService.getTrendingSearches(5).subscribe(trending => {
      this.trendingSearches = trending;
    });

    // Load recent searches if user is authenticated
    this.searchService.getSearchHistory(5).subscribe(history => {
      this.recentSearches = history.searches;
    });
  }

  // Search input handlers
  onSearchInput(event: any): void {
    const query = event.target.value || '';
    this.searchQuery = query;
    this.searchSubject.next(query);
    
    if (query.length > 0) {
      this.showSuggestions = true;
    }
  }

  onSearchFocus(): void {
    this.showSuggestions = true;
    if (!this.searchQuery) {
      // Load trending and recent searches
      this.loadInitialData();
    }
  }

  onSearchBlur(): void {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
      this.showSuggestions = false;
    }, 200);
  }

  // Search actions
  performSearch(): void {
    const filters = this.getFiltersFromForm();
    this.searchService.setSearchQuery(this.searchQuery);
    this.searchService.setSearchFilters(filters);
    this.searchPerformed.emit({ query: this.searchQuery, filters });
    this.showSuggestions = false;
  }

  selectSuggestion(suggestion: SearchSuggestion): void {
    this.searchQuery = suggestion.text;
    this.searchService.setSearchQuery(suggestion.text);
    this.suggestionSelected.emit(suggestion);
    this.performSearch();
  }

  selectTrendingSearch(trending: TrendingSearch): void {
    this.searchQuery = trending.query;
    this.performSearch();
  }

  selectRecentSearch(recent: any): void {
    this.searchQuery = recent.query;
    if (recent.filters) {
      this.filtersForm.patchValue(recent.filters);
    }
    this.performSearch();
  }

  // Voice search
  startVoiceSearch(): void {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-US';
      
      recognition.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        this.searchQuery = transcript;
        this.performSearch();
      };
      
      recognition.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
      };
      
      recognition.start();
    }
  }

  // Filter handlers
  onFilterChange(): void {
    const filters = this.getFiltersFromForm();
    this.searchService.setSearchFilters(filters);
    this.filtersChanged.emit(filters);
    
    // Track filter change
    if (this.searchQuery) {
      this.searchService.trackFilterChange(this.searchQuery).subscribe();
    }
  }

  clearFilters(): void {
    this.filtersForm.reset({
      category: '',
      brand: '',
      minPrice: '',
      maxPrice: '',
      rating: '',
      inStock: false,
      onSale: false,
      sortBy: 'relevance'
    });
    this.onFilterChange();
  }

  removeFilter(filter: any): void {
    this.filtersForm.patchValue({ [filter.key]: filter.key === 'inStock' || filter.key === 'onSale' ? false : '' });
    this.onFilterChange();
  }

  clearRecentSearches(): void {
    this.searchService.clearSearchHistory('recent').subscribe(success => {
      if (success) {
        this.recentSearches = [];
      }
    });
  }

  // Helper methods
  private getFiltersFromForm(): SearchFilters {
    const formValue = this.filtersForm.value;
    const filters: SearchFilters = {};
    
    Object.keys(formValue).forEach(key => {
      const value = formValue[key];
      if (value !== '' && value !== null && value !== false) {
        (filters as any)[key] = value;
      }
    });
    
    return filters;
  }

  private updateActiveFilters(): void {
    const filters = this.getFiltersFromForm();
    this.activeFilters = [];
    
    Object.keys(filters).forEach(key => {
      const value = (filters as any)[key];
      if (value !== undefined && value !== '' && value !== false) {
        this.activeFilters.push({
          key,
          label: this.getFilterLabel(key),
          value: this.getFilterDisplayValue(key, value)
        });
      }
    });
  }

  private getFilterLabel(key: string): string {
    const labels: { [key: string]: string } = {
      category: 'Category',
      brand: 'Brand',
      minPrice: 'Min Price',
      maxPrice: 'Max Price',
      rating: 'Rating',
      inStock: 'In Stock',
      onSale: 'On Sale',
      sortBy: 'Sort'
    };
    return labels[key] || key;
  }

  private getFilterDisplayValue(key: string, value: any): string {
    if (key === 'rating') {
      return `${value}+ Stars`;
    }
    if (key === 'inStock' || key === 'onSale') {
      return 'Yes';
    }
    return value.toString();
  }

  // Template helper methods
  getSuggestionIcon(type: string): string {
    const icons: { [key: string]: string } = {
      completion: 'search',
      product: 'cube',
      brand: 'business',
      category: 'grid',
      trending: 'trending-up',
      personal: 'person'
    };
    return icons[type] || 'search';
  }

  getSuggestionTypeLabel(type: string): string {
    const labels: { [key: string]: string } = {
      completion: 'Search suggestion',
      product: 'Product',
      brand: 'Brand',
      category: 'Category',
      trending: 'Trending',
      personal: 'From your history'
    };
    return labels[type] || type;
  }

  highlightQuery(text: string): string {
    if (!this.searchQuery) return text;
    const regex = new RegExp(`(${this.searchQuery})`, 'gi');
    return text.replace(regex, '<strong>$1</strong>');
  }

  getRelativeTime(timestamp: string): string {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  }

  getSearchTime(timestamp: number): number {
    return Date.now() - timestamp;
  }

  // Track by functions for performance
  trackSuggestion(index: number, suggestion: SearchSuggestion): string {
    return suggestion.text + suggestion.type;
  }

  trackTrending(index: number, trending: TrendingSearch): string {
    return trending.query;
  }

  trackRecent(index: number, recent: any): string {
    return recent.query + recent.timestamp;
  }

  trackFilter(index: number, filter: any): string {
    return filter.key + filter.value;
  }
}
